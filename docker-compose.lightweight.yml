version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: reading-platform-postgres-light
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-reading_platform}
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    # Memory limits for lightweight deployment
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    # Optimize PostgreSQL for low memory
    command: >
      postgres
      -c shared_buffers=64MB
      -c effective_cache_size=128MB
      -c maintenance_work_mem=32MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER:-postgres}']
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: reading-platform-redis-light
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    # Memory limits and Redis optimization
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
    command: >
      redis-server
      --maxmemory 100mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile.lightweight
    container_name: reading-platform-api-light
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-password}@postgres:5432/${POSTGRES_DB:-reading_platform}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_SPEECH_KEY=${AZURE_SPEECH_KEY}
      - AZURE_SPEECH_REGION=${AZURE_SPEECH_REGION}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-http://localhost:3000}
      - LOG_LEVEL=${LOG_LEVEL:-WARNING}
      # Python memory optimization
      - PYTHONOPTIMIZE=1
      - PYTHONDONTWRITEBYTECODE=1
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped

  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile.lightweight
    container_name: reading-platform-web-light
    ports:
      - "80:80"
    depends_on:
      - api
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
