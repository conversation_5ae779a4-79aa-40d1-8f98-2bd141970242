# Production Environment Variables
# Copy this file to .env on your server and update the values

# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_very_secure_password_here
POSTGRES_DB=reading_platform
DATABASE_URL=******************************************************************/reading_platform

# Redis
REDIS_URL=redis://redis:6379/0

# Security
SECRET_KEY=your_very_secure_secret_key_here_at_least_32_characters_long
ACCESS_TOKEN_EXPIRE_MINUTES=480
REFRESH_TOKEN_EXPIRE_MINUTES=43200

# API Keys - Translation Services
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_CLOUD_PROJECT_ID=your_google_project_id
GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/google-credentials.json

# API Keys - TTS Services
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_region
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-southeast-1

# CORS and Security
BACKEND_CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Email (Optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Reading Platform

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
TTS_OUTPUT_FORMAT=mp3

# Logging
LOG_LEVEL=INFO
