#!/bin/bash

# Reading Platform Production Deployment Script
# Usage: ./deploy.sh [domain_name]

set -e

DOMAIN=${1:-"your-domain.com"}
SERVER_USER=${2:-"root"}
SERVER_IP=${3:-"your-server-ip"}

echo "🚀 Deploying Reading Platform to production..."
echo "Domain: $DOMAIN"
echo "Server: $SERVER_USER@$SERVER_IP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Requirements check passed ✅"
}

# Build and deploy locally (for testing)
deploy_local() {
    print_status "Building and deploying locally..."
    
    # Copy production environment file
    if [ ! -f .env ]; then
        cp .env.production .env
        print_warning "Created .env from .env.production template. Please update the values!"
        print_warning "Edit .env file with your actual configuration before continuing."
        read -p "Press Enter to continue after editing .env file..."
    fi
    
    # Build and start services
    docker-compose -f docker-compose.prod.yml down
    docker-compose -f docker-compose.prod.yml build --no-cache
    docker-compose -f docker-compose.prod.yml up -d
    
    print_status "Local deployment completed! 🎉"
    print_status "Frontend: http://localhost"
    print_status "API: http://localhost:8000"
    print_status "API Docs: http://localhost:8000/docs"
}

# Deploy to remote server
deploy_remote() {
    print_status "Deploying to remote server: $SERVER_USER@$SERVER_IP"
    
    # Create deployment directory on server
    ssh $SERVER_USER@$SERVER_IP "mkdir -p /opt/reading-platform"
    
    # Copy files to server
    print_status "Copying files to server..."
    rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'venv' \
        ./ $SERVER_USER@$SERVER_IP:/opt/reading-platform/
    
    # Run deployment on server
    ssh $SERVER_USER@$SERVER_IP "cd /opt/reading-platform && ./deploy.sh local"
    
    print_status "Remote deployment completed! 🎉"
    print_status "Your application should be available at: http://$SERVER_IP"
}

# Setup SSL with Let's Encrypt
setup_ssl() {
    print_status "Setting up SSL certificate for $DOMAIN..."
    
    # Install certbot if not present
    if ! command -v certbot &> /dev/null; then
        print_status "Installing certbot..."
        apt-get update
        apt-get install -y certbot python3-certbot-nginx
    fi
    
    # Stop nginx temporarily
    docker-compose -f docker-compose.prod.yml stop web
    
    # Get SSL certificate
    certbot certonly --standalone -d $DOMAIN -d www.$DOMAIN
    
    # Copy certificates to ssl directory
    mkdir -p ssl
    cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ssl/
    cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ssl/
    
    # Update nginx configuration for SSL
    # This would require updating the nginx.conf file
    
    # Restart services
    docker-compose -f docker-compose.prod.yml up -d
    
    print_status "SSL setup completed! 🔒"
}

# Main deployment logic
case "${1:-local}" in
    "local")
        check_requirements
        deploy_local
        ;;
    "remote")
        if [ -z "$SERVER_IP" ]; then
            print_error "Server IP is required for remote deployment"
            echo "Usage: ./deploy.sh remote [server_ip] [user]"
            exit 1
        fi
        check_requirements
        deploy_remote
        ;;
    "ssl")
        setup_ssl
        ;;
    *)
        echo "Usage: $0 {local|remote|ssl}"
        echo "  local  - Deploy locally using Docker"
        echo "  remote - Deploy to remote server"
        echo "  ssl    - Setup SSL certificate"
        exit 1
        ;;
esac
